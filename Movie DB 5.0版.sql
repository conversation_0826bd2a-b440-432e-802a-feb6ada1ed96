--创建数据库
CREATE DATABASE MovieDB;
GO

USE MovieDB;
GO

--用户表（Users）
CREATE TABLE Users (
    UserID INT PRIMARY KEY IDENTITY(1,1),--用户ID
    UserName NVARCHAR(50) NOT NULL UNIQUE,--用户名字
    UserPassword NVARCHAR(50) NOT NULL,--用户密码
    UserPhone NVARCHAR(20),--电话号码
    RegisterDate DATETIME DEFAULT GETDATE()--注册时间
);

--Users插入
INSERT INTO Users (UserName, UserPassword, UserPhone, RegisterDate)
VALUES 
('张三', 'zhangsan123', '10000123000', '2022-02-23 03:10:00'),
('李四', 'lisi456', '10000456000', '2023-03-15 10:30:00'),
('王五', 'wangwu789', '10000789000', '2024-04-21 01:30:00'),
('赵六', 'zhaoliu012', '10000012000', '2025-05-06 09:50:00');


--管理员表（Admins）
CREATE TABLE Admins (
    AdminID INT PRIMARY KEY IDENTITY(1,1),--管理员ID
    AdminName NVARCHAR(50) NOT NULL UNIQUE,--管理员姓名
    AdminPassword NVARCHAR(50) NOT NULL--管理员密码
);

--Admins插入
INSERT INTO Admins (AdminName, AdminPassword)
VALUES 
('admin1', 'a001001'),
('admin2', 'a002002'), 
('admin3', 'a003003'), 
('admin4', 'a004004');

--影院表（Cinemas）
CREATE TABLE Cinemas (
    CinemaID INT PRIMARY KEY IDENTITY(1,1),--影院ID
    CinemaName NVARCHAR(100),--影院名称
    Region NVARCHAR(100),--影院地区
    CinemaAddress NVARCHAR(255),--影院地址
    CinemaPhone NVARCHAR(20),--影院联系方式
    HallCount INT,--影厅数量
);

--Cinemas插入
INSERT INTO Cinemas (CinemaName, Region, CinemaAddress, CinemaPhone, HallCount)
VALUES
('万达影城(福州仓山万达广场店)', '福州市仓山区', '福州市仓山区浦上大道276号仓山万达广场（2号门进）第四层', '010-11335577', 6),
('国际影城(福州祥禾天地店)', '福州市高新区', '福州市高新区乌龙江南大道69号19号楼', '010-22446688', 5),
('星星影城(福清福和店)', '福清市福和路', '福清市福和路九号福和广场4层4F-A', '020-12345678', 3);

--影厅表（Halls）
CREATE TABLE Halls (
    HallID INT PRIMARY KEY IDENTITY(1,1),--影厅ID
    CinemaID INT FOREIGN KEY REFERENCES Cinemas(CinemaID),--影院ID
    HallName NVARCHAR(50),--影厅名称
    SeatCount INT,--座位数
	SeatM INT,--M排座位
	SeatN INT--N个座位
);

--Halls插入
INSERT INTO Halls (CinemaID, HallName, SeatCount, SeatM, SeatN)
VALUES
-- 万达影城(福州仓山万达店) - CinemaID 1 --6个厅
(1, '巨幕厅1', 320, 20, 16),--20*16
(1, '杜比厅1', 200, 20, 10),--20*10
(1, '杜比厅2', 200, 20, 10),
(1, '激光厅1', 80, 10, 8),--10*8
(1, '激光厅2', 80, 10, 8),
(1, '激光厅3', 80, 10, 8),

-- 国际影城(福州祥禾天地店) - CinemaID 2 --5个厅
(2, 'IMAX厅1', 320, 20, 16),--20*16
(2, 'IMAX厅2', 320, 20, 16),
(2, '中型厅1', 200, 20, 10),--20*10
(2, '中型厅2', 200, 20, 10),
(2, '小型厅1', 80, 10, 8),--10*8

-- 星星影城(福清福和店) - CinemaID 3 --3个厅
(3, 'STARMAX厅', 320, 20, 16),--20*16
(3, 'Gold Class厅', 200, 20, 10),--20*10
(3, 'STARSMALL厅', 80, 10, 8);--10*8


--电影分类表（MovieCategory）
CREATE TABLE MovieCategory (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),--分类ID
    CategoryName NVARCHAR(50)--分类名称
);

-- MovieCategory插入
INSERT INTO MovieCategory (CategoryName)
VALUES 
('动作'),
('故事'),
('科幻'),
('喜剧');



--电影信息表（Movies）
CREATE TABLE Movies (
    MovieID INT PRIMARY KEY IDENTITY(1,1),--电影ID
    MovieName NVARCHAR(100),--电影名称
    MovieDescription NVARCHAR(MAX),--影片描述
    CategoryID INT FOREIGN KEY REFERENCES MovieCategory(CategoryID),--电影类别
    ReleaseDate DATE,--发布日期
    Duration INT, -- 片长（分钟）
    
    IsHot BIT DEFAULT 0,--是否热映
    IsComingSoon BIT DEFAULT 0,--是否待映
	MovieImage NVARCHAR(255)--电影海报
);



-- 往Movies插入
INSERT INTO Movies (MovieName, MovieDescription, CategoryID, ReleaseDate, Duration, IsHot, IsComingSoon, MovieImage)
VALUES 
-- 动作类（CategoryID=1）
('死侍与金刚狼', '死侍与金刚狼联手开启疯狂冒险，打破第四面墙的R级动作喜剧', 1, '2024-07-26', 127, 0, 0, 'Images/Movie1.jpg'),
('误判', '一起贩毒冤案让被告人面临27年牢狱之灾，曾是干探的检控官霍子豪凭其敏锐触觉和执着，抽丝剥茧展开调查', 1, ' 2024-12-27', 118, 1, 0, 'Images/Movie2.jpg'),
('美国队长4', '能够展翅高飞的猎鹰山姆·威尔逊受史蒂夫·罗杰斯的信任所托，接过盾牌，正式成为美国队长', 1, '2025-02-14', 139,  1, 0,'Images/Movie3.jpg'),
('碟中谍8：最终清算', 'IMF小队不仅要面对全知全能又无影无形的“智体”与其手下盖布瑞尔的百般阻拦，还要解决来自过去的种种恩怨', 1, '2025-05-30', 170, 0, 0, 'Images/Movie4.jpg'),

-- 故事类（CategoryID=2）
('长安的荔枝', '某天有人安排给李善德一个“荔枝使”的肥差，只要办成，那就是荣华富贵人生逆袭，但要是办不成……', 2, '2025-07-18', 122, 0, 1,'Images/Movie5.jpg'),
('水饺皇后', '无依无靠的臧健和独自一人扛下重重压力，为冲破命运的束缚，她决定卖水饺谋生', 2, '2025-04-30', 119, 1, 0, 'Images/Movie6.jpg'),
('南京照相馆', '故事取材于南京大屠杀期间日军真实罪证影像', 2, '2025-07-25', 137, 0, 1, 'Images/Movie7.jpg'),
('小小的我', '患有脑瘫的刘春和勇敢冲破身心的枷锁', 2, ' 2024-12-27', 131, 1, 0, 'Images/Movie8.jpg'),

-- 科幻类（CategoryID=3）
('哥斯拉大战金刚2：帝国崛起', '金刚和哥斯拉将再度联手对抗一个潜伏在世界中的巨大威胁，并逐步探索这些巨兽们的起源以及骷髅岛等神秘地方的奥秘', 3, '2024-03-29', 115, 0, 0,'Images/Movie9.jpg'),
('沙丘2', '保罗在沙漠星球带领弗瑞曼人反抗哈克南家族', 3, '2024-03-08', 166, 0, 0, 'Images/Movie10.jpg'),
('新·驯龙高手', '翻拍自经典动画电影《驯龙高手》。影片从一位少年和飞龙的奇妙相遇展开，讲述了主角小嗝嗝在与没牙仔在朝夕相处的热血冒险中，逐渐为千百年来矛盾重重的两族重塑和平的故事。', 3, '2025-06-05', 130, 1, 0, 'Images/Movie11.jpg'),
('侏罗纪世界：重生', '为获取神秘基因，一支精英小队潜入恐龙禁地，然而等待他们的却是无法预知的危机和惊天阴谋', 3, '2025-07-02', 134, 0, 0, 'Images/default‎.jpg'),

-- 喜剧类（CategoryID=4）
('热辣滚烫', '人生至暗时刻，随波逐流的女孩是否决心逆流而上？', 4, '2024-02-10', 129, 0, 0, 'Images/Movie13.jpg'),
('抓娃娃', '“汤里没油，兜里没子”的马成钢和春兰，赶驴打工，家徒四壁，而儿子马继业则是他们逆天改命的唯一希望。', 4, '2024-07-16', 133, 0, 0,'Images/Movie14.jpg'),
('戏台', '台上霸王声声唱，台下荒唐众生相，既要保住戏班饭碗，又要哄好台下观众，大幕拉开之后，这场戏到底要怎么唱？', 4, '2025-07-25', 117, 0, 1, 'Images/Movie15.jpg'),
('唐探1900', '1900年，在美国旧金山唐人街，华裔印第安猎人阿鬼与留美青年秦福因一场凶杀案偶然结识，误打误撞组成“唐人街神探”组合', 4, '2025-01-29', 136,  1, 0,'Images/Movie16.jpg');



--场次信息表（Schedules）
CREATE TABLE Schedules (
    ScheduleID INT PRIMARY KEY IDENTITY(1,1),--场次ID
    MovieID INT FOREIGN KEY REFERENCES Movies(MovieID),--电影ID
    HallID INT FOREIGN KEY REFERENCES Halls(HallID),--影厅ID
    StartTime DATETIME,--开始时间
	EndTime DATETIME,--结束时间
    Price DECIMAL(6,2),--单价
    AvailableSeats INT--可订座位
);

-- Schedules插入
INSERT INTO Schedules (MovieID, HallID, StartTime, EndTime, Price, AvailableSeats)
VALUES 
-- 万达影城(福州仓山万达店) - CinemaID 1（HallID 1-6）
-- 死侍与金刚狼（MovieID=1）
(1, 1, '2024-07-26 10:00:00', DATEADD(MINUTE, 127, '2024-07-26 10:00:00'), 69.90, 320),
-- 误判（MovieID=2）
(2, 2, '2024-12-27 10:00:00', DATEADD(MINUTE, 118, '2024-12-27 10:00:00'), 59.90, 200),
-- 美国队长4（MovieID=3）
(3, 3, '2025-02-14 10:00:00', DATEADD(MINUTE, 139, '2025-02-14 10:00:00'), 59.90, 200),
-- 碟中谍8：最终清算（MovieID=4）
(4, 4, '2025-05-30 10:00:00', DATEADD(MINUTE, 170, '2025-05-30 10:00:00'), 49.90, 80),
-- 长安的荔枝（MovieID=5）
(5, 5, '2025-07-18 10:00:00', DATEADD(MINUTE, 122, '2025-07-18 10:00:00'), 49.90, 80),
-- 水饺皇后（MovieID=6）
(6, 6, '2025-04-30 10:00:00', DATEADD(MINUTE, 119, '2025-04-30 10:00:00'), 39.90, 80),
-- 南京照相馆（MovieID=7）
(7, 1, '2025-07-25 14:00:00', DATEADD(MINUTE, 137, '2025-07-25 14:00:00'), 69.90, 320),
-- 小小的我（MovieID=8）
(8, 2, '2024-12-27 14:00:00', DATEADD(MINUTE, 131, '2024-12-27 14:00:00'), 59.90, 200),
-- 哥斯拉大战金刚2（MovieID=9）
(9, 3, '2024-03-29 14:00:00', DATEADD(MINUTE, 115, '2024-03-29 14:00:00'), 59.90, 200),
-- 沙丘2（MovieID=10）
(10, 4, '2024-03-08 14:00:00', DATEADD(MINUTE, 166, '2024-03-08 14:00:00'), 49.90, 80),
-- 超人（MovieID=11）
(11, 5, '2025-07-11 14:00:00', DATEADD(MINUTE, 130, '2025-07-11 14:00:00'), 49.90, 80),
-- 侏罗纪世界：重生（MovieID=12）
(12, 6, '2025-07-02 14:00:00', DATEADD(MINUTE, 134, '2025-07-02 14:00:00'), 39.90, 80),
-- 热辣滚烫（MovieID=13）
(13, 1, '2024-02-10 18:00:00', DATEADD(MINUTE, 129, '2024-02-10 18:00:00'), 69.90, 320),
-- 抓娃娃（MovieID=14）
(14, 2, '2024-07-16 18:00:00', DATEADD(MINUTE, 133, '2024-07-16 18:00:00'), 59.90, 200),
-- 戏台（MovieID=15）
(15, 3, '2025-07-25 18:00:00', DATEADD(MINUTE, 117, '2025-07-25 18:00:00'), 59.90, 200),
-- 唐探1900（MovieID=16）
(16, 4, '2025-01-29 18:00:00', DATEADD(MINUTE, 136, '2025-01-29 18:00:00'), 49.90, 80),

-- 国际影城(福州祥禾天地店) - CinemaID 2（HallID 7-11）
-- 死侍与金刚狼（MovieID=1）
(1, 7, '2024-07-26 10:00:00', DATEADD(MINUTE, 127, '2024-07-26 10:00:00'), 89.90, 320),
-- 误判（MovieID=2）
(2, 8, '2024-12-27 10:00:00', DATEADD(MINUTE, 118, '2024-12-27 10:00:00'), 79.90, 320),
-- 美国队长4（MovieID=3）
(3, 9, '2025-02-14 10:00:00', DATEADD(MINUTE, 139, '2025-02-14 10:00:00'), 54.90, 200),
-- 碟中谍8：最终清算（MovieID=4）
(4, 10, '2025-05-30 10:00:00', DATEADD(MINUTE, 170, '2025-05-30 10:00:00'), 44.90, 200),
-- 长安的荔枝（MovieID=5）
(5, 11, '2025-07-18 10:00:00', DATEADD(MINUTE, 122, '2025-07-18 10:00:00'), 79.90, 80),
-- 水饺皇后（MovieID=6）
(6, 7, '2025-04-30 14:00:00', DATEADD(MINUTE, 119, '2025-04-30 14:00:00'), 89.90, 320),
-- 南京照相馆（MovieID=7）
(7, 8, '2025-07-25 14:00:00', DATEADD(MINUTE, 137, '2025-07-25 14:00:00'), 79.90, 320),
-- 小小的我（MovieID=8）
(8, 9, '2024-12-27 14:00:00', DATEADD(MINUTE, 131, '2024-12-27 14:00:00'), 54.90, 200),
-- 哥斯拉大战金刚2（MovieID=9）
(9, 10, '2024-03-29 14:00:00', DATEADD(MINUTE, 115, '2024-03-29 14:00:00'), 44.90, 200),
-- 沙丘2（MovieID=10）
(10, 11, '2024-03-08 14:00:00', DATEADD(MINUTE, 166, '2024-03-08 14:00:00'), 79.90, 80),
-- 超人（MovieID=11）
(11, 7, '2025-07-11 18:00:00', DATEADD(MINUTE, 130, '2025-07-11 18:00:00'), 89.90, 320),
-- 侏罗纪世界：重生（MovieID=12）
(12, 8, '2025-07-02 18:00:00', DATEADD(MINUTE, 134, '2025-07-02 18:00:00'), 79.90, 320),
-- 热辣滚烫（MovieID=13）
(13, 9, '2024-02-10 18:00:00', DATEADD(MINUTE, 129, '2024-02-10 18:00:00'), 54.90, 200),
-- 抓娃娃（MovieID=14）
(14, 10, '2024-07-16 18:00:00', DATEADD(MINUTE, 133, '2024-07-16 18:00:00'), 44.90, 200),
-- 戏台（MovieID=15）
(15, 11, '2025-07-25 18:00:00', DATEADD(MINUTE, 117, '2025-07-25 18:00:00'), 79.90, 80),
-- 唐探1900（MovieID=16）
(16, 7, '2025-01-29 20:00:00', DATEADD(MINUTE, 136, '2025-01-29 20:00:00'), 89.90, 80),

-- 星星影城(福清福和店) - CinemaID 3（HallID 12-14）
-- 死侍与金刚狼（MovieID=1）
(1, 12, '2024-07-26 10:00:00', DATEADD(MINUTE, 127, '2024-07-26 10:00:00'), 89.90, 320),
-- 误判（MovieID=2）
(2, 13, '2024-12-27 10:00:00', DATEADD(MINUTE, 118, '2024-12-27 10:00:00'), 79.90, 200),
-- 美国队长4（MovieID=3）
(3, 14, '2025-02-14 10:00:00', DATEADD(MINUTE, 139, '2025-02-14 10:00:00'), 64.90, 80),
-- 碟中谍8：最终清算（MovieID=4）
(4, 12, '2025-05-30 14:00:00', DATEADD(MINUTE, 170, '2025-05-30 14:00:00'), 89.90, 320),
-- 长安的荔枝（MovieID=5）
(5, 13, '2025-07-18 14:00:00', DATEADD(MINUTE, 122, '2025-07-18 14:00:00'), 79.90, 200),
-- 水饺皇后（MovieID=6）
(6, 14, '2025-04-30 14:00:00', DATEADD(MINUTE, 119, '2025-04-30 14:00:00'), 64.90, 80),
-- 南京照相馆（MovieID=7）
(7, 12, '2025-07-25 18:00:00', DATEADD(MINUTE, 137, '2025-07-25 18:00:00'), 89.90, 320),
-- 小小的我（MovieID=8）
(8, 13, '2024-12-27 18:00:00', DATEADD(MINUTE, 131, '2024-12-27 18:00:00'), 79.90, 200),
-- 哥斯拉大战金刚2（MovieID=9）
(9, 14, '2024-03-29 18:00:00', DATEADD(MINUTE, 115, '2024-03-29 18:00:00'), 64.90, 80),
-- 沙丘2（MovieID=10）
(10, 12, '2024-03-08 18:00:00', DATEADD(MINUTE, 166, '2024-03-08 18:00:00'), 89.90, 320),
-- 超人（MovieID=11）
(11, 13, '2025-07-11 20:00:00', DATEADD(MINUTE, 130, '2025-07-11 20:00:00'), 79.90, 200),
-- 侏罗纪世界：重生（MovieID=12）
(12, 14, '2025-07-02 20:00:00', DATEADD(MINUTE, 134, '2025-07-02 20:00:00'), 64.90, 80),
-- 热辣滚烫（MovieID=13）
(13, 12, '2024-02-10 20:00:00', DATEADD(MINUTE, 129, '2024-02-10 20:00:00'), 89.90, 320),
-- 抓娃娃（MovieID=14）
(14, 13, '2024-07-16 20:00:00', DATEADD(MINUTE, 133, '2024-07-16 20:00:00'), 79.90, 200),
-- 戏台（MovieID=15）
(15, 14, '2025-07-25 20:00:00', DATEADD(MINUTE, 117, '2025-07-25 20:00:00'), 64.90, 80),
-- 唐探1900（MovieID=16）
(16, 12, '2025-01-29 22:00:00', DATEADD(MINUTE, 136, '2025-01-29 22:00:00'), 89.90, 320);


--订单表（Orders）
CREATE TABLE Orders (
    OrderID INT PRIMARY KEY IDENTITY(1,1),--订单ID
    UserID INT FOREIGN KEY REFERENCES Users(UserID),--预定用户ID
    ScheduleID INT FOREIGN KEY REFERENCES Schedules(ScheduleID),--场次ID
    OrderTime DATETIME DEFAULT GETDATE(),--预定时间
    OrderSeatM INT,--预定座位第M排
	OrderSeatN INT,--预定座位第N个
    TotalPrice DECIMAL(8,2)--总价
);

-- Orders插入
INSERT INTO Orders (UserID, ScheduleID, OrderTime, OrderSeatM, OrderSeatN, TotalPrice)
VALUES 
-- 张三（UserID=1）的订单
(1, 1, '2024-07-25 09:30:00', 1,1, 69.90),   -- 万达影城：死侍与金刚狼（1张,69.90）
(1, 11, '2025-07-10 15:00:00', 1,1, 49.90),  -- 国际影城：超人（1张，49.90）
(1, 19, '2024-03-07 10:00:00', 1,1, 89.90),  -- 国际影城：沙丘2（1张，89.90）
(1, 13, '2024-02-09 14:20:00', 1,1, 69.90),   -- 万达影城：热辣滚烫（1张,69.90）

-- 李四（UserID=2）的订单
(2, 2, '2024-12-26 15:30:00', 1,2, 59.90),   -- 万达影城：误判（1张，59.90）
(2, 14, '2024-07-15 11:20:00', 1,2, 59.90),   -- 万达影城：抓娃娃（1张,59.90）
(2, 27, '2025-02-13 16:40:00', 1,2, 64.90),  -- 星星影城：美国队长4（1张，64.90）
(2, 31, '2025-01-28 19:00:00', 1,2, 89.90),  -- 星星影城：唐探1900（1张，89.90）

-- 王五（UserID=3）的订单
(3, 3, '2025-02-13 10:00:00', 1,3, 59.90),   -- 万达影城：美国队长4（1张，59.90）
(3, 29, '2024-03-28 09:45:00', 1,3, 64.90),   -- 星星影城：哥斯拉大战金刚2（1张,64.90）
(3, 15, '2025-04-29 16:00:00', 1,3, 89.90),  -- 国际影城：水饺皇后（1张，89.90）
(3, 21, '2025-07-24 14:00:00', 1,3, 69.90),  -- 国际影城：南京照相馆（1张，69.90）

-- 赵六（UserID=4）的订单
(4, 4, '2025-05-29 16:20:00', 1,4, 49.90),    -- 万达影城：碟中谍8（1张，49.90）
(4, 32, '2025-07-01 14:00:00', 1,4, 69.90),  -- 星星影城：侏罗纪世界：重生（1张，69.90）
(4, 24, '2024-12-26 20:10:00', 1,4, 79.90),   -- 星星影城：小小的我（1张,79.90）
(4, 5, '2025-07-17 19:30:00', 1,4, 49.90);    -- 万达影城：长安的荔枝（1张，49.90）


--评分表（Ratings）
CREATE TABLE Ratings (
    RatingID INT PRIMARY KEY IDENTITY(1,1),--评分ID
    MovieID INT FOREIGN KEY REFERENCES Movies(MovieID),--电影ID
    UserID INT FOREIGN KEY REFERENCES Users(UserID),--用户ID
    Score INT CHECK(Score BETWEEN 1 AND 10),--评分数值
    RatingTime DATETIME DEFAULT GETDATE()--评分时间
);


-- Ratings插入
INSERT INTO Ratings (MovieID, UserID, Score, RatingTime)
VALUES 
-- 张三（UserID=1）的评分
(1, 1, 9, '2024-07-26 13:00:00'),
(11, 1, 8, '2025-07-11 21:00:00'),
(10, 1, 10, '2024-03-08 18:00:00'),
(13, 1, 7, '2024-02-10 21:30:00'),

-- 李四（UserID=2）的评分
(2, 2, 8, '2024-12-27 13:30:00'),
(14, 2, 6, '2024-07-16 21:00:00'),
(3, 2, 9, '2025-02-14 14:30:00'),
(16, 2, 7, '2025-01-30 00:30:00'),

-- 王五（UserID=3）的评分
(3, 3, 8, '2025-02-14 14:00:00'),
(9, 3, 9, '2024-03-29 20:30:00'),
(6, 3, 7, '2025-04-30 13:00:00'),
(7, 3, 10, '2025-07-25 18:00:00'),

-- 赵六（UserID=4）的评分
(4, 4, 9, '2025-05-30 14:00:00'),
(12, 4, 8, '2025-07-02 23:00:00'),
(8, 4, 7, '2024-12-27 21:30:00'),
(5, 4, 6, '2025-07-18 13:00:00');



--电影完整信息视图（含分类）
CREATE VIEW v_MovieFullInfo AS
SELECT 
    m.MovieID,
    m.MovieName,
    m.MovieDescription,
    mc.CategoryName,
    m.ReleaseDate,
    m.Duration,
    m.IsHot,
    m.IsComingSoon,
	m.MovieImage
FROM Movies m
INNER JOIN MovieCategory mc ON m.CategoryID = mc.CategoryID;

--影院与影厅关联视图（首页影城 已完成 勿动）
CREATE VIEW v_CinemaWithHalls AS
SELECT 
    c.CinemaID,
    c.CinemaName,
    c.Region,
    c.CinemaAddress,
    c.CinemaPhone,
    c.HallCount AS TotalHall,
    h.HallID,
    h.HallName,
    h.SeatCount AS HallSeats,
	s.ScheduleID,
	s.MovieID,
	s.StartTime,
	m.MovieName
FROM Cinemas c
INNER JOIN Halls h ON c.CinemaID = h.CinemaID
INNER JOIN Schedules s ON h.HallID = s.HallID
INNER JOIN Movies m ON s.MovieID = m.MovieID;


drop VIEW v_ScheduleFullInfo
--场次完整信息视图（购票核心）（勿动）
CREATE VIEW v_ScheduleFullInfo AS
SELECT 
    s.ScheduleID,
    m.MovieID,
    m.MovieName,
    mc.CategoryName,
    c.CinemaID,
    c.CinemaName,
    c.Region,
    h.HallID,
    h.HallName,
	h.SeatM,
	h.SeatN,
    s.StartTime,
    s.EndTime,
    s.Price,
    s.AvailableSeats,
    h.SeatCount AS TotalSeats,
    h.SeatCount - s.AvailableSeats AS SoldSeats, -- 已售座位数
    DATEDIFF(MINUTE, s.StartTime, s.EndTime) AS ShowDuration -- 放映时长
FROM Schedules s
INNER JOIN Movies m ON s.MovieID = m.MovieID
INNER JOIN MovieCategory mc ON m.CategoryID = mc.CategoryID
INNER JOIN Halls h ON s.HallID = h.HallID
INNER JOIN Cinemas c ON h.CinemaID = c.CinemaID;

--用户电影订单详情视图
CREATE VIEW v_UserMovieOrderDetails AS
SELECT 
    o.OrderID,
    o.UserID,
    u.UserName,
    u.UserPhone,
    o.ScheduleID,
    m.MovieName,
    c.CinemaName,
    h.HallName,
    s.StartTime,
    o.OrderSeatM,
	o.OrderSeatN,
    o.TotalPrice,
    o.OrderTime,
    -- 订单状态（根据放映时间判断）
    CASE 
        WHEN s.EndTime < GETDATE() THEN '已完成'
        WHEN s.StartTime > GETDATE() THEN '未开始'
        ELSE '放映中'
    END AS OrderStatus
FROM Orders o
INNER JOIN Users u ON o.UserID = u.UserID
INNER JOIN Schedules s ON o.ScheduleID = s.ScheduleID
INNER JOIN Movies m ON s.MovieID = m.MovieID
INNER JOIN Halls h ON s.HallID = h.HallID
INNER JOIN Cinemas c ON h.CinemaID = c.CinemaID;

--电影评分统计视图
CREATE VIEW v_MovieRatingSummary AS
SELECT 
    m.MovieID,
    m.MovieName,
    COUNT(r.RatingID) AS TotalRatings, -- 评分总人数
    ISNULL(ROUND(AVG(r.Score * 1.0), 1), 0) AS AvgScore, -- 平均评分
    MAX(r.Score) AS MaxScore, -- 最高评分
    MIN(r.Score) AS MinScore -- 最低评分
FROM Movies m
LEFT JOIN Ratings r ON m.MovieID = r.MovieID
GROUP BY m.MovieID, m.MovieName;


--热映电影视图（快速筛选IsHot=1）
CREATE VIEW v_HotMovies AS
SELECT * FROM v_MovieFullInfo WHERE IsHot = 1;

--待映电影视图（快速筛选IsComingSoon=1）
CREATE VIEW v_ComingSoonMovies AS
SELECT * FROM v_MovieFullInfo WHERE IsComingSoon = 1;

--管理员影院运营统计视图
CREATE VIEW v_AdminCinemaStats AS
SELECT 
    c.CinemaID,
    c.CinemaName,
    c.Region,
    COUNT(DISTINCT s.ScheduleID) AS TotalSchedules, -- 总场次
    SUM(h.SeatCount) AS TotalSeats, -- 总座位数
    SUM(h.SeatCount - s.AvailableSeats) AS TotalSoldSeats, -- 总售票数
    -- 上座率（保留1位小数）
    ROUND(
        CASE WHEN SUM(h.SeatCount) = 0 THEN 0 
        ELSE (SUM(h.SeatCount - s.AvailableSeats) * 1.0 / SUM(h.SeatCount)) * 100 
        END, 1
    ) AS OccupancyRate
FROM Cinemas c
INNER JOIN Halls h ON c.CinemaID = h.CinemaID
LEFT JOIN Schedules s ON h.HallID = s.HallID
GROUP BY c.CinemaID, c.CinemaName, c.Region;

--管理员电影销售统计视图（统计每部电影的售票量和票房）
CREATE VIEW v_AdminMovieSales AS
SELECT 
    m.MovieID,
    m.MovieName,
    mc.CategoryName,
    COUNT(DISTINCT o.OrderID) AS OrderCount, -- 订单数
    SUM(o.SeatCount) AS TotalTicketsSold, -- 总售票数
    SUM(o.TotalPrice) AS TotalBoxOffice -- 总票房
FROM Movies m
INNER JOIN MovieCategory mc ON m.CategoryID = mc.CategoryID
LEFT JOIN Schedules s ON m.MovieID = s.MovieID
LEFT JOIN Orders o ON s.ScheduleID = o.ScheduleID
GROUP BY m.MovieID, m.MovieName, mc.CategoryName;


--查询某电影的全部场次 存储过程
CREATE PROCEDURE sp_GetSchedulesByMovie
    @MovieID INT
AS
BEGIN
    SELECT s.ScheduleID, c.CinemaName, h.HallName, s.StartTime, s.Price
    FROM Schedules s
    JOIN Halls h ON s.HallID = h.HallID
    JOIN Cinemas c ON h.CinemaID = c.CinemaID
    WHERE s.MovieID = @MovieID
END;

--根据地区模糊查询影院 存储过程
CREATE PROCEDURE sp_SearchCinemasByRegion
    @keyword NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT 
        CinemaID,
        CinemaName,
        Region,
        CinemaAddress,
        CinemaPhone,
        HallCount
    FROM Cinemas
    WHERE Region LIKE '%' + @keyword + '%'
    ORDER BY Region, CinemaName;
END;
GO

--购票后减少余票数 触发器
CREATE TRIGGER trg_UpdateAvailableSeats
ON Orders
AFTER INSERT
AS
BEGIN
    -- 确保不返回受影响行数的额外信息
    SET NOCOUNT ON;

    -- 当有新订单插入时，更新对应场次的可订座位数
    UPDATE Schedules
    SET AvailableSeats = AvailableSeats - 1
    -- 从插入的记录中获取对应的场次ID
    FROM Schedules
    INNER JOIN inserted ON Schedules.ScheduleID = inserted.ScheduleID;
    
    -- 可选：检查是否有座位不足的情况并抛出错误
    IF EXISTS (
        SELECT 1 
        FROM Schedules
        INNER JOIN inserted ON Schedules.ScheduleID = inserted.ScheduleID
        WHERE Schedules.AvailableSeats < 0
    )
    BEGIN
        RAISERROR ('错误：场次座位已售罄，无法完成预订', 16, 1);
        ROLLBACK TRANSACTION;
    END
END;

--drop TRIGGER trg_DecreaseSeatsOnOrder

--模糊搜索电影
CREATE PROCEDURE SearchMovies
    @keyword NVARCHAR(100)
AS
BEGIN
    SELECT * FROM Movies WHERE MovieName LIKE '%' + @keyword + '%'
END

--exec sp_GetSchedulesByMovie @MovieID = '3';
