<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A9836A84-AAF2-4508-A8F5-03C81A04A578}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>MovieSystem</RootNamespace>
    <AssemblyName>MovieSystem</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BLL\AdminBLL.cs" />
    <Compile Include="BLL\BuyMovieBLL.cs" />
    <Compile Include="BLL\CinemaBLL.cs" />
    <Compile Include="BLL\MovieBLL.cs" />
    <Compile Include="BLL\UserBLL.cs" />
    <Compile Include="BuyTicketForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BuyTicketForm.Designer.cs">
      <DependentUpon>BuyTicketForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CinemaControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CinemaControl.Designer.cs">
      <DependentUpon>CinemaControl.cs</DependentUpon>
    </Compile>
    <Compile Include="DAL\AdminDAL.cs" />
    <Compile Include="DAL\BuyMovieDAL.cs" />
    <Compile Include="DAL\CinemaDAL.cs" />
    <Compile Include="DAL\DBHelper.cs" />
    <Compile Include="DAL\MovieDAL.cs" />
    <Compile Include="DAL\UserDAL.cs" />
    <Compile Include="HomeControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="HomeControl.Designer.cs">
      <DependentUpon>HomeControl.cs</DependentUpon>
    </Compile>
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\Admin.cs" />
    <Compile Include="Models\Movie.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="ProfileControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ProfileControl.Designer.cs">
      <DependentUpon>ProfileControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RegisterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RegisterForm.Designer.cs">
      <DependentUpon>RegisterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ShopControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ShopControl.Designer.cs">
      <DependentUpon>ShopControl.cs</DependentUpon>
    </Compile>
    <Compile Include="UserMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UserMainForm.Designer.cs">
      <DependentUpon>UserMainForm.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="BuyTicketForm.resx">
      <DependentUpon>BuyTicketForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CinemaControl.resx">
      <DependentUpon>CinemaControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HomeControl.resx">
      <DependentUpon>HomeControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Include="RegisterForm.resx">
      <DependentUpon>RegisterForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShopControl.resx">
      <DependentUpon>ShopControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UserMainForm.resx">
      <DependentUpon>UserMainForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="Images\Movie1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie10.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie11.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\default.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie13.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie14.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie15.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie16.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie2.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie3.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie4.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie5.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie6.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie7.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie8.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\Movie9.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>