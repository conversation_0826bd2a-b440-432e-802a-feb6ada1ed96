﻿using MovieSystem.BLL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace MovieSystem
{
    public partial class HomeControl : UserControl
    {
        private string currentUserName;
        private MovieBLL movieBLL = new MovieBLL();
        public HomeControl()
        {
            InitializeComponent();
            LoadHotMovies();
            LoadComingSoonMovies();
        }

        private void LoadHotMovies()
        {
            var hotMovies = movieBLL.GetHotMovies();
            LoadMoviesToPanel(hotMovies, flowHotMovies);
        }

        private void LoadComingSoonMovies()
        {
            var comingMovies = movieBLL.GetComingSoonMovies();
            LoadMoviesToPanel(comingMovies, flowComingSoonMovies);
        }
        private void LoadMoviesToPanel(List<Movie> movies, FlowLayoutPanel panel)
        {
            panel.Controls.Clear();

            foreach (var movie in movies)
            {
                PictureBox pic = new PictureBox();
                string imagePath = Path.Combine(Application.StartupPath, movie.MovieImage);

                if (File.Exists(imagePath))
                {
                    pic.Image = Image.FromFile(imagePath);
                }
                // 如果文件不存在，则不设置图片（pic.Image 保持为 null）

                pic.SizeMode = PictureBoxSizeMode.StretchImage;
                pic.Size = new Size(120, 180);
                pic.Margin = new Padding(10);

                ToolTip tip = new ToolTip();
                tip.SetToolTip(pic, movie.MovieName);

                panel.Controls.Add(pic);
            }
        }
        private void HomeControl_Load(object sender, EventArgs e)
        {

        }
    }
}
