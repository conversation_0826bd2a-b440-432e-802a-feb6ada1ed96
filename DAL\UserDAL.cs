﻿using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.DAL
{
    public class UserDAL
    {
        public User GetUserByNameAndPassword(string username, string password)
        {
            using (SqlConnection conn = DBHelper.GetConnection())
            {
                string sql = "SELECT * FROM Users WHERE UserName = @name AND UserPassword = @pwd";
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddWithValue("@name", username);
                cmd.Parameters.AddWithValue("@pwd", password);
                conn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                if (reader.Read())
                {
                    return new User
                    {
                        UserID = (int)reader["UserID"],
                        UserName = reader["UserName"].ToString(),
                        UserPassword = reader["UserPassword"].ToString(),
                        UserPhone = reader["UserPhone"].ToString(),
                        RegisterDate = (DateTime)reader["RegisterDate"]
                    };
                }
                return null;
            }
        }


        public int AddUser(User user)
        {
            using (SqlConnection conn = DBHelper.GetConnection())
            {
                string sql = "INSERT INTO Users (UserName, UserPassword, UserPhone, RegisterDate) VALUES (@name, @pwd, @phone, @date)";
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddWithValue("@name", user.UserName);
                cmd.Parameters.AddWithValue("@pwd", user.UserPassword);
                cmd.Parameters.AddWithValue("@phone", user.UserPhone);
                cmd.Parameters.AddWithValue("@date", user.RegisterDate);
                conn.Open();
                return cmd.ExecuteNonQuery();
            }
        }
    }
}
