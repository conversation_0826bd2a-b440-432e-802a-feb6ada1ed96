﻿using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.DAL
{
    public class MovieDAL
    {
        private string connectionString = "Data Source=.;Initial Catalog=MovieDB;Integrated Security=True";

        public List<Movie> GetMoviesByCondition(string condition)
        {
            List<Movie> movies = new List<Movie>();
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                string sql = $"SELECT * FROM Movies WHERE {condition}";
                SqlCommand cmd = new SqlCommand(sql, conn);
                conn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    Movie m = new Movie
                    {
                        MovieID = Convert.ToInt32(reader["MovieID"]),
                        MovieName = reader["MovieName"].ToString(),
                        MovieDescription = reader["MovieDescription"].ToString(),
                        MovieImage = reader["MovieImage"].ToString()
                    };
                    movies.Add(m);
                }
            }
            return movies;
        }
    }
}
