﻿using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.DAL
{
    public class AdminDAL
    {
        public Admin GetAdminByNameAndPassword(string adminName, string password)
        {
            using (SqlConnection conn = DBHelper.GetConnection())
            {
                string sql = "SELECT * FROM Admins WHERE AdminName = @name AND AdminPassword = @pwd";
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddWithValue("@name", adminName);
                cmd.Parameters.AddWithValue("@pwd", password);
                conn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                if (reader.Read())
                {
                    return new Admin
                    {
                        AdminID = (int)reader["AdminID"],
                        AdminName = reader["AdminName"].ToString(),
                        AdminPassword = reader["AdminPassword"].ToString()
                    };
                }
                return null;
            }
        }

        public int AddAdmin(Admin admin)
        {
            using (SqlConnection conn = DBHelper.GetConnection())
            {
                string sql = "INSERT INTO Admins (AdminName, AdminPassword) VALUES (@name, @pwd)";
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddWithValue("@name", admin.AdminName);
                cmd.Parameters.AddWithValue("@pwd", admin.AdminPassword);
                conn.Open();
                return cmd.ExecuteNonQuery();
            }
        }
    }
}
