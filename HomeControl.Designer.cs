﻿namespace MovieSystem
{
    partial class HomeControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(HomeControl));
            this.grpHotMovies = new System.Windows.Forms.GroupBox();
            this.flowHotMovies = new System.Windows.Forms.FlowLayoutPanel();
            this.grpComingSoonMovies = new System.Windows.Forms.GroupBox();
            this.flowComingSoonMovies = new System.Windows.Forms.FlowLayoutPanel();
            this.grpHotMovies.SuspendLayout();
            this.grpComingSoonMovies.SuspendLayout();
            this.SuspendLayout();
            // 
            // grpHotMovies
            // 
            this.grpHotMovies.BackColor = System.Drawing.Color.Maroon;
            this.grpHotMovies.Controls.Add(this.flowHotMovies);
            this.grpHotMovies.Font = new System.Drawing.Font("苹方 中等", 13.8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.grpHotMovies.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.grpHotMovies.Location = new System.Drawing.Point(140, 38);
            this.grpHotMovies.Name = "grpHotMovies";
            this.grpHotMovies.Size = new System.Drawing.Size(769, 279);
            this.grpHotMovies.TabIndex = 0;
            this.grpHotMovies.TabStop = false;
            this.grpHotMovies.Text = "热映";
            // 
            // flowHotMovies
            // 
            this.flowHotMovies.AutoScroll = true;
            this.flowHotMovies.Location = new System.Drawing.Point(22, 34);
            this.flowHotMovies.Name = "flowHotMovies";
            this.flowHotMovies.Size = new System.Drawing.Size(730, 235);
            this.flowHotMovies.TabIndex = 0;
            this.flowHotMovies.WrapContents = false;
            // 
            // grpComingSoonMovies
            // 
            this.grpComingSoonMovies.BackColor = System.Drawing.Color.Maroon;
            this.grpComingSoonMovies.Controls.Add(this.flowComingSoonMovies);
            this.grpComingSoonMovies.Font = new System.Drawing.Font("苹方 中等", 13.8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.grpComingSoonMovies.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.grpComingSoonMovies.Location = new System.Drawing.Point(140, 342);
            this.grpComingSoonMovies.Name = "grpComingSoonMovies";
            this.grpComingSoonMovies.Size = new System.Drawing.Size(769, 285);
            this.grpComingSoonMovies.TabIndex = 1;
            this.grpComingSoonMovies.TabStop = false;
            this.grpComingSoonMovies.Text = "待映";
            // 
            // flowComingSoonMovies
            // 
            this.flowComingSoonMovies.AutoScroll = true;
            this.flowComingSoonMovies.Location = new System.Drawing.Point(22, 30);
            this.flowComingSoonMovies.Name = "flowComingSoonMovies";
            this.flowComingSoonMovies.Size = new System.Drawing.Size(730, 239);
            this.flowComingSoonMovies.TabIndex = 1;
            this.flowComingSoonMovies.WrapContents = false;
            // 
            // HomeControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("$this.BackgroundImage")));
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.Controls.Add(this.grpComingSoonMovies);
            this.Controls.Add(this.grpHotMovies);
            this.Name = "HomeControl";
            this.Size = new System.Drawing.Size(1076, 767);
            this.Load += new System.EventHandler(this.HomeControl_Load);
            this.grpHotMovies.ResumeLayout(false);
            this.grpComingSoonMovies.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox grpHotMovies;
        private System.Windows.Forms.GroupBox grpComingSoonMovies;
        private System.Windows.Forms.FlowLayoutPanel flowHotMovies;
        private System.Windows.Forms.FlowLayoutPanel flowComingSoonMovies;
    }
}
