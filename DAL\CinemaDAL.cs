﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.DAL
{
    public class CinemaDAL
    {
        private string connectionString = "Data Source=.;Initial Catalog=MovieDB;Integrated Security=True";

        // 获取所有影城列表
        public DataTable GetAllCinemas()
        {
            string sql = "SELECT DISTINCT CinemaID, CinemaName FROM v_CinemaWithHalls";
            using (SqlConnection conn = new SqlConnection(connectionString))
            using (SqlDataAdapter da = new SqlDataAdapter(sql, conn))
            {
                DataTable dt = new DataTable();
                da.Fill(dt);
                return dt;
            }
        }

        // 获取指定影城的详细信息
        public DataTable GetCinemaDetails(int cinemaId)
        {
            string sql = "SELECT * FROM v_CinemaWithHalls WHERE CinemaID = @CinemaID";
            using (SqlConnection conn = new SqlConnection(connectionString))
            using (SqlDataAdapter da = new SqlDataAdapter(sql, conn))
            {
                da.SelectCommand.Parameters.AddWithValue("@CinemaID", cinemaId);
                DataTable dt = new DataTable();
                da.Fill(dt);
                return dt;
            }
        }
    }
}
