﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.DAL
{
    public class BuyMovieDAL
    {
        private string connectionString = "Data Source=.;Initial Catalog=MovieDB;Integrated Security=True";

        // 获取所有电影（只取图片和名字）
        public DataTable GetAllMovies()
        {
            string sql = "SELECT MovieID, MovieName, MovieImage FROM Movies";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlDataAdapter da = new SqlDataAdapter(sql, conn);
                DataTable dt = new DataTable();
                da.Fill(dt);
                return dt;
            }
        }

        // 获取某电影详细信息
        public DataRow GetMovieDetails(int movieId)
        {
            string sql = "SELECT * FROM Movies WHERE MovieID = @MovieID";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlDataAdapter da = new SqlDataAdapter(sql, conn);
                da.SelectCommand.Parameters.AddWithValue("@MovieID", movieId);
                DataTable dt = new DataTable();
                da.Fill(dt);
                if (dt.Rows.Count > 0)
                    return dt.Rows[0];
                return null;
            }
        }

        // 获取某电影的排片信息（用你写的视图）
        public DataTable GetSchedulesByMovie(int movieId)
        {
            string sql = "SELECT * FROM v_ScheduleFullInfo WHERE MovieID = @MovieID";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlDataAdapter da = new SqlDataAdapter(sql, conn);
                da.SelectCommand.Parameters.AddWithValue("@MovieID", movieId);
                DataTable dt = new DataTable();
                da.Fill(dt);
                return dt;
            }
        }

        // 检查座位是否已被占用
        public bool IsSeatTaken(int scheduleId, int row, int col)
        {
            string sql = "SELECT COUNT(*) FROM Orders WHERE ScheduleID = @ScheduleID AND OrderSeatM = @Row AND OrderSeatN = @Col";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddWithValue("@ScheduleID", scheduleId);
                cmd.Parameters.AddWithValue("@Row", row);
                cmd.Parameters.AddWithValue("@Col", col);
                conn.Open();
                int count = (int)cmd.ExecuteScalar();
                return count > 0;
            }
        }

        // 提交订单
        public bool InsertOrder(int scheduleId, int userId, int row, int col, decimal price)
        {
            string sql = @"INSERT INTO Orders (ScheduleID, UserID, OrderSeatM, OrderSeatN, OrderTime, TotalPrice)
                           VALUES (@ScheduleID, @UserID, @Row, @Col, GETDATE(), @Price)";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddWithValue("@ScheduleID", scheduleId);
                cmd.Parameters.AddWithValue("@UserID", userId);
                cmd.Parameters.AddWithValue("@Row", row);
                cmd.Parameters.AddWithValue("@Col", col);
                cmd.Parameters.AddWithValue("@Price", price);
                conn.Open();
                return cmd.ExecuteNonQuery() > 0;
            }
        }
    }
}
