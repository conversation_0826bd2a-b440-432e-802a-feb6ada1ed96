﻿using MovieSystem.BLL;
using MovieSystem.DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.VisualStyles;

namespace MovieSystem
{
    public partial class BuyTicketForm : Form
    {
        private int movieId;
        private BuyMovieBLL buymovieBLL = new BuyMovieBLL();
        private int selectedScheduleId;
        private decimal selectedPrice;
        private int userId = 1; // 测试用，实际从登录用户传入

        public BuyTicketForm(int movieId)
        {
            InitializeComponent();
            this.movieId = movieId;
            LoadMovieDetails();
            LoadSchedules();

            cboSchedule.SelectedIndexChanged += cboSchedule_SelectedIndexChanged;  // 绑定下拉框事件
            btnSubmit.Click += btnSubmit_Click;  // 绑定按钮点击事件
        }

        private void LoadMovieDetails()
        {
            DataRow row = buymovieBLL.GetMovieDetails(movieId);
            if (row != null)
            {
                picMovie.Image = Image.FromFile(row["MovieImage"].ToString());
                lblName.Text = row["MovieName"].ToString();
                lblDuration.Text = row["Duration"] + " 分钟";
                txtDescription.Text = row["MovieDescription"].ToString();
            }
        }

        private void LoadSchedules()
        {
            DataTable schedules = buymovieBLL.GetSchedulesByMovie(movieId);
            cboSchedule.DataSource = schedules;
            cboSchedule.DisplayMember = "CinemaName";
            cboSchedule.ValueMember = "ScheduleID";

            if (cboSchedule.Items.Count > 0)
                cboSchedule.SelectedIndex = 0;  // 触发SelectedIndexChanged，显示票价
        }

        
        private void cboSchedule_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cboSchedule.SelectedItem == null) return;
            DataRowView drv = (DataRowView)cboSchedule.SelectedItem;
            selectedScheduleId = Convert.ToInt32(drv["ScheduleID"]);
            selectedPrice = Convert.ToDecimal(drv["Price"]);
            lblPrice.Text = "票价：" + selectedPrice.ToString("0.00") + " 元";
            //lblPrice.Text="票价："+selectedPrice;
            
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            try
            {
                int row = int.Parse(txtSeatM.Text.Trim());
                int col = int.Parse(txtSeatN.Text.Trim());

                bool success = buymovieBLL.BuyTicket(selectedScheduleId, userId, row, col, selectedPrice);

                if (success)
                    MessageBox.Show("订票成功！");
                else
                    MessageBox.Show("订票失败：座位已被占用或余票不足。");
            }
            catch (Exception ex)
            {
                MessageBox.Show("提交失败：" + ex.Message);
            }
        }

        //private void btnSubmit_Click(object sender, EventArgs e)
        //{
        //    int row = Convert.ToInt32(txtSeatM.Text);
        //    int col = Convert.ToInt32(txtSeatN.Text);

        //    bool success = buymovieBLL.BuyTicket(selectedScheduleId, userId, row, col, selectedPrice);

        //    if (success)
        //        MessageBox.Show("订票成功！");
        //    else
        //        MessageBox.Show("订票失败：座位已被占用或余票不足。");
        //}

        private void BuyTicketForm_Load(object sender, EventArgs e)
        {

        }
    }
}
