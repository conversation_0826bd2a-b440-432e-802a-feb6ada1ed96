﻿using MovieSystem.DAL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.BLL
{
    public class AdminBLL
    {
        private AdminDAL dal = new AdminDAL();

        public Admin Login(string name, string password)
        {
            return dal.GetAdminByNameAndPassword(name, password);
        }

        public bool Register(Admin admin)
        {
            return dal.AddAdmin(admin) > 0;
        }
    }
}
