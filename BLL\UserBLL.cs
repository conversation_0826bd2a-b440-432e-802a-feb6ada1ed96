﻿using MovieSystem.DAL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.BLL
{
    public class UserBLL
    {
        private UserDAL dal = new UserDAL();

        public User Login(string username, string password)
        {
            return dal.GetUserByNameAndPassword(username, password);
        }

        public bool Register(User user)
        {
            return dal.AddUser(user) > 0;
        }
    }
}
