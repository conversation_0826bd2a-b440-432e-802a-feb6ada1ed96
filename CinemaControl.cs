﻿using MovieSystem.BLL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MovieSystem
{
    public partial class CinemaControl : UserControl
    {
        private CinemaBLL cinemaBLL = new CinemaBLL();

        public CinemaControl()
        {
            InitializeComponent();
            LoadCinemaButtons();
        }

        // 加载左侧影城按钮
        private void LoadCinemaButtons()
        {
            DataTable cinemas = cinemaBLL.GetAllCinemas();
            foreach (DataRow row in cinemas.Rows)
            {
                int cinemaId = Convert.ToInt32(row["CinemaID"]);
                string cinemaName = row["CinemaName"].ToString();

                Button btn = new Button();
                btn.Text = cinemaName;
                btn.Tag = cinemaId; // 保存ID
                btn.Width = flpCinemas.Width - 10;
                btn.Height = 75;
                btn.Margin = new Padding(5);
                btn.BackColor = Color.IndianRed;
                btn.Click += (s, e) => ShowCinemaDetails(cinemaId);

                flpCinemas.Controls.Add(btn);
            }
        }

        // 显示右侧影城详情
        private void ShowCinemaDetails(int cinemaId)
        {
            DataTable dt = cinemaBLL.GetCinemaDetails(cinemaId);
            if (dt.Rows.Count > 0)
            {
                // 基本信息（取第一行）
                lblName.Text = "影城名称: " + dt.Rows[0]["CinemaName"].ToString();
                lblRegion.Text = "地区: " + dt.Rows[0]["Region"].ToString();
                lblAddress.Text = "地址: " + dt.Rows[0]["CinemaAddress"].ToString();

                // 影厅和场次信息
                DataTable hallSchedules = new DataTable();
                hallSchedules.Columns.Add("影厅名称");
                hallSchedules.Columns.Add("座位数");
                hallSchedules.Columns.Add("影片名");
                hallSchedules.Columns.Add("开始时间");

                foreach (DataRow row in dt.Rows)
                {
                    hallSchedules.Rows.Add(
                        row["HallName"].ToString(),
                        row["HallSeats"].ToString(),
                        row["MovieName"].ToString(),
                        Convert.ToDateTime(row["StartTime"]).ToString("yyyy-MM-dd HH:mm")
                    );
                }

                dgvHallSchedules.DataSource = hallSchedules;
            }
        }
    }
}
