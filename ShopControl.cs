﻿using MovieSystem.BLL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MovieSystem
{
    public partial class ShopControl : UserControl
    {
        private BuyMovieBLL buymovieBLL = new BuyMovieBLL();

        public ShopControl()
        {
            InitializeComponent();
            btnMovie.Click += BtnMovie_Click;
            LoadMovies(); // 默认显示电影
        }

        private void BtnMovie_Click(object sender, EventArgs e)
        {
            LoadMovies();
        }

        

        private void LoadMovies()
        {
            DataTable movies = buymovieBLL.GetAllMovies();
            flpShop.Controls.Clear();
            foreach (DataRow row in movies.Rows)
            {
                string imgRelativePath = row["MovieImage"].ToString();
                string imgFullPath = Path.Combine(Application.StartupPath, imgRelativePath);
                AddItemCard(row["MovieName"].ToString(), imgFullPath, (int)row["MovieID"], true);
            }
        }

       
        private void AddItemCard(string name, string imagePath, int id, bool isMovie)
        {
            Panel panel = new Panel { Width = 150, Height = 200, Margin = new Padding(10) };

            // 路径校验和默认图处理
            string validImagePath = imagePath;
            if (string.IsNullOrWhiteSpace(imagePath)
                || imagePath.IndexOfAny(System.IO.Path.GetInvalidPathChars()) >= 0
                || !System.IO.File.Exists(imagePath))
            {
                // 默认图片路径（确保这个文件存在于你的程序目录中）
                validImagePath = System.IO.Path.Combine(Application.StartupPath, "images", "default.jpg");
            }

            PictureBox pic = new PictureBox
            {
                Image = Image.FromFile(validImagePath),
                SizeMode = PictureBoxSizeMode.StretchImage,
                Width = 150,
                Height = 220,
                Tag = id
            };

            pic.Click += (s, e) =>
            {
                if (isMovie)
                    new BuyTicketForm(id).ShowDialog();
                else
                    MessageBox.Show($"购买 {name} 功能未实现");
            };

            Label lbl = new Label
            {
                Text = name,
                Dock = DockStyle.Bottom,
                TextAlign = ContentAlignment.MiddleCenter
            };

            panel.Controls.Add(pic);
            panel.Controls.Add(lbl);
            flpShop.Controls.Add(panel);
        }

        
        private void ShopControl_Load(object sender, EventArgs e)
        {

        }
    }
}
