﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;


namespace MovieSystem
{
    public partial class UserMainForm : Form
    {
        private string currentUserName;
        public UserMainForm(string userName)
        {
            InitializeComponent();
            currentUserName = userName;
        }

        private HomeControl homeControl;
        private CinemaControl cinemaControl;
        private ShopControl shopControl;
        private ProfileControl profileControl;

        
        private void UserMainForm_Load(object sender, EventArgs e)
        {
            lblWelcome.Text = "欢迎你！" + currentUserName;

            homeControl = new HomeControl();
            cinemaControl = new CinemaControl();
            shopControl = new ShopControl();
            profileControl = new ProfileControl();

            homeControl.Dock = DockStyle.Fill;
            cinemaControl.Dock = DockStyle.Fill;
            shopControl.Dock = DockStyle.Fill;
            profileControl.Dock = DockStyle.Fill;

            panelMain.Controls.Add(homeControl);
            panelMain.Controls.Add(cinemaControl);
            panelMain.Controls.Add(shopControl);
            panelMain.Controls.Add(profileControl);

            //LoadControl(homeControl); // 默认加载首页
            ShowControl(homeControl);
        }

        //private void LoadControl(UserControl control)
        //{
        //    panelMain.Controls.Clear();         // panelMain 是主面板容器，确保它存在
        //    control.Dock = DockStyle.Fill;
        //    panelMain.Controls.Add(control);
        //}
        //private void LoadHomePage()
        //{
        //    HomeControl home = new HomeControl();
        //    panelMain.Controls.Clear();
        //    home.Dock = DockStyle.Fill;
        //    panelMain.Controls.Add(home);
        //}

        private void ShowControl(UserControl control)
        {
            foreach (Control c in panelMain.Controls)
            {
                c.Visible = false;
            }
            control.Visible = true;
        }

        private void btnHome_Click(object sender, EventArgs e)
        {
            ShowControl(homeControl);
        }

        private void btnCinema_Click(object sender, EventArgs e)
        {
            ShowControl(cinemaControl);
        }

        private void btnShop_Click(object sender, EventArgs e)
        {
            ShowControl(shopControl);
        }

        private void btnProfile_Click(object sender, EventArgs e)
        {
            ShowControl(profileControl);
        }
    }
}
