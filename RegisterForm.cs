﻿using MovieSystem.BLL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MovieSystem
{
    public partial class RegisterForm : Form
    {
        public RegisterForm()
        {
            InitializeComponent();
            cmbRole.Items.AddRange(new string[] { "用户", "管理员" });
            cmbRole.SelectedIndex = 0;

            cmbRole.SelectedIndexChanged += cmbRole_SelectedIndexChanged;
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            string username = txtUserName.Text.Trim();
            string password = txtPassword.Text.Trim();
            string phone = txtPhone.Text.Trim();
            string role = cmbRole.SelectedItem?.ToString();

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) || role == null)
            {
                MessageBox.Show("请填写完整信息！");
                return;
            }

            if (role == "用户")
            {
                User newUser = new User
                {
                    UserName = username,
                    UserPassword = password,
                    UserPhone = phone,
                    RegisterDate = DateTime.Now
                };

                bool success = new UserBLL().Register(newUser);
                if (success)
                {
                    MessageBox.Show("用户注册成功！");
                    this.Close();
                }
                else
                {
                    MessageBox.Show("注册失败，请重试！");
                }
            }
            else if (role == "管理员")
            {
                Admin newAdmin = new Admin
                {
                    AdminName = username,
                    AdminPassword = password
                };

                bool success = new AdminBLL().Register(newAdmin);
                if (success)
                {
                    MessageBox.Show("管理员注册成功！");
                    this.Close();
                }
                else
                {
                    MessageBox.Show("注册失败，请重试！");
                }
            }
        }

        private void cmbRole_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedRole = cmbRole.SelectedItem.ToString();
            if (selectedRole == "管理员")
            {
                txtPhone.Visible = false;
                lblPhone.Visible = false; // 确保手机号的标签命名为 lblPhone
            }
            else
            {
                txtPhone.Visible = true;
                lblPhone.Visible = true;
            }
        }

        private void RegisterForm_Load(object sender, EventArgs e)
        {

        }
    }
}
