﻿using MovieSystem.BLL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MovieSystem
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUserName.Text.Trim();
            string password = txtPassword.Text.Trim();
            string role = cmbRole.SelectedItem?.ToString();

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) || role == null)
            {
                MessageBox.Show("请填写完整信息！");
                return;
            }

            if (role == "用户")
            {
                var user = new UserBLL().Login(username, password);
                if (user != null)
                {
                    MessageBox.Show("用户登录成功！");
                    string userName = txtUserName.Text.Trim();
                    UserMainForm mainForm = new UserMainForm(userName);
                    mainForm.Show();
                    this.Hide();
                }
                else
                {
                    MessageBox.Show("登录失败！");
                }
            }
            else if (role == "管理员")
            {
                var admin = new AdminBLL().Login(username, password);
                if (admin != null)
                {
                    MessageBox.Show("管理员登录成功！");
                    //new AdminMainForm().Show();
                    //this.Hide();
                }
                else
                {
                    MessageBox.Show("登录失败！");
                }
            }
        }

        private void btnRegister_Click(object sender, EventArgs e)
        {
            RegisterForm registerForm = new RegisterForm();
            registerForm.ShowDialog(); // 模态打开注册窗口，注册完成后返回
        }
    }
}
