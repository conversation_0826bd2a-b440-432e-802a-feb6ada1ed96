﻿using MovieSystem.DAL;
using MovieSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.BLL
{
    public class MovieBLL
    {
        private MovieDAL movieDAL = new MovieDAL();

        public List<Movie> GetHotMovies()
        {
            return movieDAL.GetMoviesByCondition("IsHot = 1");
        }

        public List<Movie> GetComingSoonMovies()
        {
            return movieDAL.GetMoviesByCondition("IsComingSoon = 1");
        }
    }
}
