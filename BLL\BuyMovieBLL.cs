﻿using MovieSystem.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MovieSystem.BLL
{
    public class BuyMovieBLL
    {
        private BuyMovieDAL dal = new BuyMovieDAL();

        public DataTable GetAllMovies() => dal.GetAllMovies();

        public DataRow GetMovieDetails(int movieId) => dal.GetMovieDetails(movieId);

        public DataTable GetSchedulesByMovie(int movieId) => dal.GetSchedulesByMovie(movieId);

        public bool BuyTicket(int scheduleId, int userId, int row, int col, decimal price)
        {
            // 检查座位是否已被占用
            if (dal.IsSeatTaken(scheduleId, row, col))
                return false;

            // 插入订单
            return dal.InsertOrder(scheduleId, userId, row, col, price);
        }
    }
}
